{"name": "gf-auto-archive", "version": "1.0.0", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node src/index.js", "build": "node build.js", "script": "node run.js"}, "keywords": ["playwright", "automation", "web-scraping"], "author": "", "license": "ISC", "description": "网页自动化脚本工具", "pkg": {"scripts": "src/**/*.js", "assets": ["data/**/*", "node_modules/playwright/**/*"], "targets": ["node18-win-x64"], "outputPath": "dist"}, "dependencies": {"chalk": "^4.1.2", "commander": "^14.0.0", "inquirer": "^8.2.6", "ora": "^5.4.1", "playwright": "^1.54.1", "winston": "^3.17.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "^24.0.15", "eslint": "^9.32.0", "pkg": "^5.8.1"}}