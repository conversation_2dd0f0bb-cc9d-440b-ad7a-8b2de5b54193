const logger = require('../utils/logger');
const { formatTime } = require('../utils/util');
const SELECTORS = require('../utils/config/selectors');
const {
  downloadPDFFile,
  clickAndPrint,
  downloadFromURL,
} = require('../utils/pdfDownloader');
const { getText } = require('../utils/core/ElementOperator');

/**
 * 提款申请处理器 - 核心业务逻辑
 */
class WithdrawalProcessor {
  constructor(elementOp, navigationManager, fileManager, configInstance) {
    this.elementOp = elementOp;
    this.navigationManager = navigationManager;
    this.fileManager = fileManager;
    this.configInstance = configInstance;
  }

  /**
   * 处理详情页面
   */
  async processDetailPage() {
    // 等待页面稳定
    await this.elementOp.page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await this.elementOp.page.waitForTimeout(5000);

    logger.info('提取客户信息和申请号');
    // 提取页面数据
    const customerName = await this.elementOp.getText(
      SELECTORS.detail.customerName,
    );
    const LoanTransactionTime = await this.elementOp.getText(
      SELECTORS.detail.LoanTransactionTime,
    );
    const withdrawalNumber = await this.elementOp.getText(
      SELECTORS.detail.withdrawalNumber,
    );

    // 提取融资申请号
    const rongZiNumber = await this.elementOp.getText(
      SELECTORS.detail.rongZiNumber,
    );

    logger.info('创建归档目录结构');
    // 创建目录结构
    const archiveDir = this.configInstance.get('customSettings.archiveDir');
    if (!archiveDir) {
      logger.warn('配置中未找到archiveDir，跳过目录创建');
      return;
    }

    const dirs = this.fileManager.createDirectoryStructure(
      archiveDir,
      customerName,
      LoanTransactionTime,
      withdrawalNumber,
      rongZiNumber,
    );

    logger.info('下载相关文档');
    // // 下载文件
    // await downloadPDFFile(
    //   SELECTORS.detail.financingApplicationLink[0],
    //   this.elementOp.page,
    //   dirs.withdrawalNumberDir,
    //   '融资申请书',
    // );

    // await downloadPDFFile(
    //   SELECTORS.detail.loan,
    //   this.elementOp.page,
    //   dirs.withdrawalNumberDir,
    //   '业务付款审批流程',
    // );

    // await clickAndPrint(
    //   SELECTORS.detail.print,
    //   this.elementOp.page,
    //   dirs.withdrawalNumberDir,
    //   '放款水单',
    // );

    // // 寻找关联合同下的应收帐款转让通知函
    // await this.getGuanLianContractInfo(dirs);

    // // 获取基础交易合同
    // await this.getBasicContractFile(dirs);

    await this.getZhongDengReport();

    logger.success('文件下载完成');
  }

  // 获取中登查重报告
  async getZhongDengReport() {
    const zhongDengRows = await this.elementOp.page
      .locator(
        'p.commonTitleLine:has-text("中登查重") + * .el-table__body-wrapper tbody tr',
      )
      .all();

    console.log(`找到 ${zhongDengRows.length} 行中登查重数据`);

    for (let i = 0; i < zhongDengRows.length; i++) {
      const row = zhongDengRows[i];

      try {
        // 获取当前行的"更多"按钮的 aria-controls 属性
        const moreButton = row.locator('button[aria-haspopup="list"]');
        const dropdownId = await moreButton.getAttribute('aria-controls');

        if (!dropdownId) {
          console.error(`第 ${i + 1} 行未找到下拉菜单ID`);
          continue;
        }

        // 直接通过 JavaScript 触发鼠标事件
        await row.evaluate((rowElement, index) => {
          const moreButton = rowElement.querySelector(
            'button[aria-haspopup="list"]',
          );
          if (moreButton) {
            // 触发鼠标进入事件
            const mouseEnterEvent = new MouseEvent('mouseenter', {
              bubbles: true,
              cancelable: true,
              view: window,
            });
            moreButton.dispatchEvent(mouseEnterEvent);

            // 稍后触发点击事件
            setTimeout(() => {
              const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
              });
              moreButton.dispatchEvent(clickEvent);
            }, 100);
          }
        }, i);

        await this.elementOp.page.waitForTimeout(1000);

        // 使用精确的下拉菜单ID查找"查看结果"按钮
        const viewResultButton = this.elementOp.page.locator(
          `#${dropdownId} .el-dropdown-menu__item:has-text("查看结果")`,
        );

        await viewResultButton.waitFor({ state: 'visible', timeout: 5000 });
        await viewResultButton.click();

        console.log(`已处理第 ${i + 1} 行`);

        // 关闭弹窗
        await this.elementOp.page.keyboard.press('Escape');
        await this.elementOp.page.waitForTimeout(500);
      } catch (error) {
        console.error(`处理第 ${i + 1} 行失败:`, error.message);
      }
    }
  }

  /**
   * 获取基础交易合同
   */
  async getBasicContractFile(dirs) {
    try {
      await this.elementOp.click(SELECTORS.detail.ziChanDetailBtn);

      // 等待弹窗加载
      await this.elementOp.page.waitForTimeout(2000);

      await this.elementOp.click(SELECTORS.detail.ziChanFileListBtn);

      // 等待文件列表加载
      await this.elementOp.page.waitForTimeout(3000);
      const contractElement = this.elementOp.page.locator(
        SELECTORS.detail.jiChuJiaoYiContract,
      );
      await contractElement.waitFor({ state: 'attached', timeout: 10000 });

      const len = await contractElement.count();

      for (let i = 0; i < len; i++) {
        // 先获取当前行的HTML结构
        const currentRow = contractElement.nth(i);

        // 尝试不同的选择器
        const allTds = currentRow.locator('td');
        const tdCount = await allTds.count();

        if (tdCount >= 2) {
          // 获取第二个td的内容
          const secondTd = allTds.nth(1);

          // 尝试获取.cell元素
          const linkElement = secondTd.locator('.cell span.linkTxt');

          const fileName = await linkElement.textContent();
          const success = await downloadPDFFile(
            linkElement,
            this.elementOp.page,
            dirs.rongZiNumberDir,
            fileName,
          );
          if (!success) {
            throw new Error('基础交易合同下载失败');
          }
        }
      }

      // 关闭弹窗（通过ESC键或点击关闭按钮）
      try {
        await this.elementOp.page.keyboard.press('Escape');
        await this.elementOp.page.waitForTimeout(500);
      } catch (closeError) {
        logger.warn('关闭弹窗失败，可能已自动关闭');
      }

      return true;
    } catch (error) {
      logger.error('获取基础交易合同失败', error);
      throw error;
    }
  }

  /**
   * 获取关联合同信息并打印所有文本
   */
  async getGuanLianContractInfo(dirs) {
    try {
      // 等待关联合同表格加载
      const contractTable = this.elementOp.page.locator(
        SELECTORS.detail.guanLianContract,
      );
      await contractTable.waitFor({ state: 'visible', timeout: 10000 });

      // 获取所有行
      const rows = contractTable.locator('tr');
      const rowCount = await rows.count();

      if (rowCount === 0) {
        logger.warn('未找到关联合同数据');
        return;
      }

      // 遍历每一行并检查第二个td的内容
      for (let i = 0; i < rowCount; i++) {
        const row = rows.nth(i);

        // 获取第二个td的文本内容
        const secondTd = row.locator('td').nth(1);
        const secondTdText = await secondTd.textContent();

        // 过滤包含"应收帐款转让通知函"的数据
        if (secondTdText.includes('应收账款转让通知函')) {
          const thirdTd = row.locator('td').nth(2);
          const contractNo = await thirdTd.textContent();

          // 尝试查找该td中的下载链接
          const downloadLink = secondTd.locator('a').first();
          const href = await downloadLink.getAttribute('href');

          const success = await downloadFromURL(
            href,
            this.elementOp.page,
            dirs.rongZiNumberDir,
            '应收账款转让通知函-' + contractNo,
          );

          if (!success) {
            logger.warn(`应收账款转让通知函下载失败: ${contractNo}`);
          }
        } else {
          logger.debug(
            `第 ${i + 1} 行第二个td不包含"应收帐款转让通知函": ${secondTdText}`,
          );
        }
      }

      return true;
    } catch (error) {
      logger.error('获取关联合同信息失败', error);
      throw error;
    }
  }

  /**
   * 查询单个提款申请号
   */
  async searchSingleWithdrawal(withdrawalNumber) {
    logger.info(`开始查询提款申请号: ${withdrawalNumber}`);
    // 填写查询条件
    await this.elementOp.fillInput(SELECTORS.search.input, withdrawalNumber);
    await this.elementOp.click(SELECTORS.search.button);
    await this.elementOp.waitForResults();

    // 等待查询结果确认
    await this.elementOp.page.waitForFunction(
      (targetNumber) => {
        const rows = document.querySelectorAll(
          '.el-table__body-wrapper tr.el-table__row',
        );
        return (
          rows.length === 1 &&
          Array.from(rows).some((row) => row.textContent.includes(targetNumber))
        );
      },
      withdrawalNumber,
      { timeout: 15000 },
    );

    logger.info('进入详情页面处理');
    // 点击详情按钮
    await this.elementOp.click(SELECTORS.search.detailButton);
    await this.elementOp.page.waitForLoadState('networkidle', {
      timeout: 10000,
    });

    // 处理详情页面
    await this.processDetailPage();

    // 返回列表页面
    await this.navigationManager.goBack();

    return true;
  }

  /**
   * 批量处理提款申请号
   */
  async processWithdrawalNumbers(numbers) {
    if (numbers.length === 0) {
      logger.warn('没有有效的提款申请号需要处理');
      return;
    }

    logger.step(`开始批量处理 ${numbers.length} 个提款申请号`, 1);
    const results = {
      total: numbers.length,
      success: 0,
      failed: 0,
      failedNumbers: [],
      startTime: Date.now(),
    };

    for (let i = 0; i < numbers.length; i++) {
      const number = numbers[i];
      logger.progress(i + 1, numbers.length, `查询 ${number}`);

      try {
        await this.searchSingleWithdrawal(number);
        results.success++;
      } catch (error) {
        logger.error(`查询提款申请号 ${number} 失败`, error);
        results.failed++;
        results.failedNumbers.push(number);
      }

      // 查询间隔
      if (i < numbers.length - 1) {
        await this.elementOp.page.waitForTimeout(2000);
      }
    }

    this.logProcessingResults(results);
    return results;
  }

  /**
   * 记录处理结果
   */
  logProcessingResults(results) {
    const duration = Date.now() - results.startTime;
    const avgTime = duration / results.total;
    const manualTimePerItem = Math.floor(Math.random() * 100 + 200) * 1000;
    const totalManualTime = manualTimePerItem * results.total;
    const savedTime = totalManualTime - duration;

    logger.success('所有提款申请号处理完成');
    logger.table(
      {
        总数: results.total,
        成功: results.success,
        失败: results.failed,
        总耗时: formatTime(duration),
        平均耗时: formatTime(avgTime),
        预计节省: formatTime(savedTime),
      },
      '查询结果统计',
    );

    if (results.failed > 0) {
      logger.table(
        results.failedNumbers.map((num, index) => `${index + 1}. ${num}`),
        '查询失败的申请号',
      );
    }
  }
}

module.exports = WithdrawalProcessor;
