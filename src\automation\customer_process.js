const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadFile } = require('../utils/pdfDownloader');
const SELECTORS = require('../utils/config/selectors');

async function getLicense(page, downloadDir, customerName) {
  try {
    // 监听新页面创建
    const newPagePromise = page.context().waitForEvent('page');
    const customer = page.locator(SELECTORS.detail.customerName);
    await customer.click();
    const customerPage = await newPagePromise;
    await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
    await customerPage.waitForTimeout(2000);

    // 获取营业执照
    const licenseLocator =
      'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div/div/a';

    const licenseEl = customerPage.locator(licenseLocator);
    const licenseCount = await licenseEl.count();
    logger.info(`找到了 ${licenseCount} 张营业执照`);

    if (licenseCount === 0) {
      logger.warn('未找到营业执照');
      await customerPage.close();
      return false;
    }

    // 下载所有营业执照
    for (let i = 0; i < licenseCount; i++) {
      try {
        const licenseElement = licenseEl.nth(i);
        const fileName = `营业执照-${customerName}-${i + 1}`;

        logger.info(`开始下载第 ${i + 1} 张营业执照: ${fileName}`);

        // 使用新的 downloadFile 函数，支持 PNG 等图片格式
        const success = await downloadFile(
          licenseElement,
          customerPage,
          downloadDir,
          fileName,
          {
            waitTime: 5000,
            fileType: 'png', // 强制指定为 PNG 格式
          },
        );

        if (success) {
          logger.success(`营业执照下载成功: ${fileName}`);
        } else {
          logger.warn(`营业执照下载失败: ${fileName}`);
        }
      } catch (downloadError) {
        logger.error(
          `下载第 ${i + 1} 张营业执照失败: ${downloadError.message}`,
        );
      }
    }

    // 关闭客户页面
    await customerPage.close();
    return true;
  } catch (error) {
    logger.error(`获取营业执照失败: ${error.message}`);
    return false;
  }
}

module.exports = {
  getLicense,
};
