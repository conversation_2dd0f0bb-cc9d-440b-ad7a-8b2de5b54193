const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadFile } = require('../utils/pdfDownloader');
const SELECTORS = require('../utils/config/selectors');

async function getLic(page) {
  await tabGoPage(
    page,
    'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/operation-web/ctsmManage/clientDetail?customerId=1823627972496048129',
  );
  await page.waitForLoadState('networkidle', {
    timeout: 15000,
  });
  await page.waitForTimeout(3000);

  const lic = page.locator(
    'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div',
  );
  const len = await lic.count();
  logger.info(`找到了 ${len}`);
}

async function getLicense(page, downloadDir, customerName) {
  try {
    // 监听新页面创建
    const newPagePromise = page.context().waitForEvent('page');
    const customer = page.locator(SELECTORS.detail.customerName);
    await customer.click();
    const customerPage = await newPagePromise;

    // 等待页面加载
    await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
    logger.info('页面网络状态已稳定');

    // 额外等待页面渲染
    await customerPage.waitForTimeout(10000);
    logger.info('等待页面渲染完成');

    // 调试：打印页面URL和标题
    const pageUrl = customerPage.url();
    const pageTitle = await customerPage.title();
    logger.info(`当前页面URL: ${pageUrl}`);
    logger.info(`当前页面标题: ${pageTitle}`);

    // 获取 iframe 的 src 并在新页面中打开
    logger.info('开始查找 iframe...');
    const iframeLocator = customerPage.locator('#iframeContainer iframe');

    let licenseEl = null;
    let licenseCount = 0;
    let iframePage = null;

    try {
      // 等待 iframe 出现
      await iframeLocator.waitFor({ state: 'visible', timeout: 15000 });
      logger.info('找到 iframe 元素');

      // 获取 iframe 的 src 属性
      const iframeSrc = await iframeLocator.getAttribute('src');
      logger.info(`获取到 iframe src: ${iframeSrc}`);

      if (!iframeSrc) {
        logger.error('无法获取 iframe 的 src 属性');
        await customerPage.close();
        return false;
      }

      // 创建新页面并导航到 iframe 的 src
      iframePage = await customerPage.context().newPage();
      logger.info('创建新页面用于访问 iframe 内容');

      // 如果 src 是相对路径，需要构建完整 URL
      let fullUrl = iframeSrc;
      if (iframeSrc.startsWith('/') || iframeSrc.startsWith('./')) {
        const baseUrl = new URL(customerPage.url()).origin;
        fullUrl = new URL(iframeSrc, baseUrl).href;
      }

      logger.info(`导航到 iframe 页面: ${fullUrl}`);
      await iframePage.goto(fullUrl, {
        timeout: 15000,
        waitUntil: 'networkidle',
      });

      // 等待页面内容加载
      await iframePage.waitForTimeout(3000);
      logger.info('iframe 页面加载完成');

      // 在新页面中查找营业执照元素
      const licenseLocators = [
        'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div/div/a',
        'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div',
        'a[href*="license"]',
        'a[href*="营业执照"]',
        'form a',
        'a', // 最通用的选择器
      ];

      let usedSelector = '';

      // 在新页面中尝试不同的选择器
      for (const selector of licenseLocators) {
        try {
          logger.info(`在 iframe 页面中尝试选择器: ${selector}`);
          licenseEl = iframePage.locator(selector);

          // 等待元素出现（最多等待10秒）
          await licenseEl.first().waitFor({
            state: 'visible',
            timeout: 10000,
          });

          licenseCount = await licenseEl.count();
          logger.info(
            `在 iframe 页面中使用选择器 "${selector}" 找到 ${licenseCount} 个元素`,
          );

          if (licenseCount > 0) {
            usedSelector = selector;
            break;
          }
        } catch (selectorError) {
          logger.warn(
            `iframe 页面中选择器 "${selector}" 失败: ${selectorError.message}`,
          );
          continue;
        }
      }

      if (licenseCount === 0) {
        logger.warn('在 iframe 页面中未找到营业执照元素');

        // 调试：检查页面内容
        try {
          const pageContent = await iframePage.textContent('body');
          const hasLicenseText =
            pageContent.includes('营业执照') || pageContent.includes('license');
          logger.info(`iframe 页面是否包含营业执照相关文本: ${hasLicenseText}`);

          const pageLinks = await iframePage.locator('a').count();
          logger.info(`iframe 页面中链接数: ${pageLinks}`);
        } catch (debugError) {
          logger.warn(`调试 iframe 页面内容失败: ${debugError.message}`);
        }

        await iframePage.close();
        await customerPage.close();
        return false;
      }

      logger.success(
        `在 iframe 页面中成功找到 ${licenseCount} 张营业执照 (使用选择器: ${usedSelector})`,
      );
    } catch (iframeError) {
      logger.error(`处理 iframe 失败: ${iframeError.message}`);
      if (iframePage) await iframePage.close();
      await customerPage.close();
      return false;
    }

    // 下载所有营业执照
    for (let i = 0; i < licenseCount; i++) {
      try {
        const licenseElement = licenseEl.nth(i);
        const fileName = `营业执照-${customerName}-${i + 1}`;

        logger.info(`开始下载第 ${i + 1} 张营业执照: ${fileName}`);

        // 使用新的 downloadFile 函数，支持 PNG 等图片格式
        // 注意：这里传入的是 iframePage，因为元素在 iframe 页面中
        const success = await downloadFile(
          licenseElement,
          iframePage,
          downloadDir,
          fileName,
          {
            waitTime: 5000,
            fileType: 'png', // 强制指定为 PNG 格式
          },
        );

        if (success) {
          logger.success(`营业执照下载成功: ${fileName}`);
        } else {
          logger.warn(`营业执照下载失败: ${fileName}`);
        }
      } catch (downloadError) {
        logger.error(
          `下载第 ${i + 1} 张营业执照失败: ${downloadError.message}`,
        );
      }
    }

    // 关闭页面
    if (iframePage) await iframePage.close();
    await customerPage.close();
    return true;
  } catch (error) {
    logger.error(`获取营业执照失败: ${error.message}`);
    if (iframePage) await iframePage.close();
    return false;
  }
}

/**
 * 调试函数 - 用于检查营业执照页面的元素
 * @param {Page} page - Playwright页面对象
 */
async function debugLicensePage(page) {
  try {
    // 监听新页面创建
    const newPagePromise = page.context().waitForEvent('page');
    const customer = page.locator(SELECTORS.detail.customerName);
    await customer.click();
    const customerPage = await newPagePromise;

    // 等待页面加载
    await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
    await customerPage.waitForTimeout(3000);

    // 打印页面基本信息
    console.log('=== 页面调试信息 ===');
    console.log('URL:', customerPage.url());
    console.log('标题:', await customerPage.title());

    // 检查页面内容
    const pageContent = await customerPage.textContent('body');
    console.log('页面是否包含"营业执照":', pageContent.includes('营业执照'));
    console.log('页面是否包含"license":', pageContent.includes('license'));

    // 检查各种元素
    console.log('=== 元素统计 ===');
    console.log('总链接数:', await customerPage.locator('a').count());
    console.log('表单数:', await customerPage.locator('form').count());
    console.log('表单内链接数:', await customerPage.locator('form a').count());

    // 检查 iframe 并获取 src
    console.log('=== iframe 检查 ===');
    const iframeLocator = customerPage.locator('#iframeContainer iframe');
    const iframeExists = (await iframeLocator.count()) > 0;
    console.log('是否存在 iframe:', iframeExists);

    if (iframeExists) {
      try {
        await iframeLocator.waitFor({ state: 'visible', timeout: 5000 });

        // 获取 iframe 的 src
        const iframeSrc = await iframeLocator.getAttribute('src');
        console.log('iframe src:', iframeSrc);

        if (iframeSrc) {
          // 创建新页面访问 iframe 内容
          const iframePage = await customerPage.context().newPage();

          // 构建完整 URL
          let fullUrl = iframeSrc;
          if (iframeSrc.startsWith('/') || iframeSrc.startsWith('./')) {
            const baseUrl = new URL(customerPage.url()).origin;
            fullUrl = new URL(iframeSrc, baseUrl).href;
          }

          console.log('访问 iframe 页面:', fullUrl);
          await iframePage.goto(fullUrl, {
            timeout: 15000,
            waitUntil: 'networkidle',
          });
          await iframePage.waitForTimeout(2000);

          // 在 iframe 页面中尝试原始选择器
          const originalSelector =
            'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div/div/a';
          const iframeOriginalCount = await iframePage
            .locator(originalSelector)
            .count();
          console.log(
            'iframe 页面中原始选择器找到的元素数:',
            iframeOriginalCount,
          );

          // 检查 iframe 页面中的链接
          const iframeLinks = await iframePage.locator('a').count();
          console.log('iframe 页面中总链接数:', iframeLinks);

          // 打印 iframe 页面中的链接
          const iframeLinkElements = await iframePage.locator('a').all();
          console.log('=== iframe 页面中的链接 ===');
          for (let i = 0; i < Math.min(iframeLinkElements.length, 10); i++) {
            const href = await iframeLinkElements[i].getAttribute('href');
            const text = await iframeLinkElements[i].textContent();
            console.log(
              `iframe 页面链接 ${
                i + 1
              }: href="${href}", text="${text?.trim()}"`,
            );
          }

          await iframePage.close();
        }
      } catch (iframeError) {
        console.log('iframe 处理失败:', iframeError.message);
      }
    } else {
      // 如果没有 iframe，尝试原始选择器
      const originalSelector =
        'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div/div/a';
      const originalCount = await customerPage
        .locator(originalSelector)
        .count();
      console.log('主页面原始选择器找到的元素数:', originalCount);
    }

    // 打印所有链接的href属性
    const links = await customerPage.locator('a').all();
    console.log('=== 所有链接 ===');
    for (let i = 0; i < Math.min(links.length, 10); i++) {
      const href = await links[i].getAttribute('href');
      const text = await links[i].textContent();
      console.log(`链接 ${i + 1}: href="${href}", text="${text?.trim()}"`);
    }

    await customerPage.close();
  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

module.exports = {
  getLic,
  getLicense,
  debugLicensePage,
};
