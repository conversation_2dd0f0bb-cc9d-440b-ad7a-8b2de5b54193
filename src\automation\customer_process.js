const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadPDFFile } = require('../utils/pdfDownloader');
const SELECTORS = require('../utils/config/selectors');

async function getLicense(page) {
  // 监听新页面创建
  const newPagePromise = page.context().waitForEvent('page');
  const customer = page.locator(SELECTORS.detail.customerName);
  await customer.click();
  const customerPage = await newPagePromise;
  await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
  await customerPage.waitForTimeout(2000);

  // 获取营业执照
  const licenseLocator =
    'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div/div/a';

  const licenseEl = customerPage.locator(licenseLocator);
  const licenseCount = await licenseEl.count();
  logger.info(`找到了 ${licenseCount} 张}`);
  if (licenseCount === 0) return;
}

module.exports = {
  getLicense,
};
