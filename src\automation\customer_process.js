const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadFile } = require('../utils/pdfDownloader');
const SELECTORS = require('../utils/config/selectors');

async function getLicense(page, downloadDir, customerName) {
  try {
    // 监听新页面创建
    const newPagePromise = page.context().waitForEvent('page');
    const customer = page.locator(SELECTORS.detail.customerName);
    await customer.click();
    const customerPage = await newPagePromise;

    // 等待页面加载
    await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
    logger.info('页面网络状态已稳定');

    // 额外等待页面渲染
    await customerPage.waitForTimeout(10000);
    logger.info('等待页面渲染完成');

    // 调试：打印页面URL和标题
    const pageUrl = customerPage.url();
    const pageTitle = await customerPage.title();
    logger.info(`当前页面URL: ${pageUrl}`);
    logger.info(`当前页面标题: ${pageTitle}`);

    // 获取营业执照 - 使用多种选择器策略
    const licenseLocators = [
      'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div',
    ];

    let licenseEl = null;
    let licenseCount = 0;
    let usedSelector = '';

    // 尝试不同的选择器
    for (const selector of licenseLocators) {
      try {
        logger.info(`尝试选择器: ${selector}`);
        licenseEl = customerPage.locator(selector);

        // 等待元素出现（最多等待10秒）
        await licenseEl.first().waitFor({
          state: 'visible',
          timeout: 10000,
        });

        licenseCount = await licenseEl.count();
        logger.info(`使用选择器 "${selector}" 找到 ${licenseCount} 个元素`);

        if (licenseCount > 0) {
          usedSelector = selector;
          break;
        }
      } catch (selectorError) {
        logger.warn(`选择器 "${selector}" 失败: ${selectorError.message}`);
        continue;
      }
    }

    logger.success(
      `成功找到 ${licenseCount} 张营业执照 (使用选择器: ${usedSelector})`,
    );

    if (licenseCount === 0) {
      logger.warn('未找到营业执照');
      await customerPage.close();
      return false;
    }

    // 下载所有营业执照
    for (let i = 0; i < licenseCount; i++) {
      try {
        const licenseElement = licenseEl.nth(i);
        const fileName = `营业执照-${customerName}-${i + 1}`;

        logger.info(`开始下载第 ${i + 1} 张营业执照: ${fileName}`);

        // 使用新的 downloadFile 函数，支持 PNG 等图片格式
        const success = await downloadFile(
          licenseElement,
          customerPage,
          downloadDir,
          fileName,
          {
            waitTime: 5000,
            fileType: 'png', // 强制指定为 PNG 格式
          },
        );

        if (success) {
          logger.success(`营业执照下载成功: ${fileName}`);
        } else {
          logger.warn(`营业执照下载失败: ${fileName}`);
        }
      } catch (downloadError) {
        logger.error(
          `下载第 ${i + 1} 张营业执照失败: ${downloadError.message}`,
        );
      }
    }

    // 关闭客户页面
    await customerPage.close();
    return true;
  } catch (error) {
    logger.error(`获取营业执照失败: ${error.message}`);
    return false;
  }
}

/**
 * 调试函数 - 用于检查营业执照页面的元素
 * @param {Page} page - Playwright页面对象
 */
async function debugLicensePage(page) {
  try {
    // 监听新页面创建
    const newPagePromise = page.context().waitForEvent('page');
    const customer = page.locator(SELECTORS.detail.customerName);
    await customer.click();
    const customerPage = await newPagePromise;

    // 等待页面加载
    await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
    await customerPage.waitForTimeout(3000);

    // 打印页面基本信息
    console.log('=== 页面调试信息 ===');
    console.log('URL:', customerPage.url());
    console.log('标题:', await customerPage.title());

    // 检查页面内容
    const pageContent = await customerPage.textContent('body');
    console.log('页面是否包含"营业执照":', pageContent.includes('营业执照'));
    console.log('页面是否包含"license":', pageContent.includes('license'));

    // 检查各种元素
    console.log('=== 元素统计 ===');
    console.log('总链接数:', await customerPage.locator('a').count());
    console.log('表单数:', await customerPage.locator('form').count());
    console.log('表单内链接数:', await customerPage.locator('form a').count());

    // 尝试原始选择器
    const originalSelector =
      'xpath=/html/body/div[1]/div/div/div/div/section/div/div/div[2]/div/div/div[1]/div/div/div[1]/form/div/div/div/div/a';
    const originalCount = await customerPage.locator(originalSelector).count();
    console.log('原始选择器找到的元素数:', originalCount);

    // 打印所有链接的href属性
    const links = await customerPage.locator('a').all();
    console.log('=== 所有链接 ===');
    for (let i = 0; i < Math.min(links.length, 10); i++) {
      const href = await links[i].getAttribute('href');
      const text = await links[i].textContent();
      console.log(`链接 ${i + 1}: href="${href}", text="${text?.trim()}"`);
    }

    await customerPage.close();
  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

module.exports = {
  getLicense,
  debugLicensePage,
};
