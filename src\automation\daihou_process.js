const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadPDFFile } = require('../utils/pdfDownloader');

// 获取账款到期日调整&融资到期日调整
async function getZiChanDaoQiDetail(page, detailButton, dirs) {
  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await page.waitForTimeout(3000);
    const fileLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a';

    const contractNoLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[3]/div';
    const fileElement = page.locator(fileLocator);

    const elementCount = await fileElement.count();

    if (elementCount === 0) {
      return;
    }
    // const fileName = await detailPage.locator(fileLocator).textContent();
    const contractNo = await page.locator(contractNoLocator).textContent();
    const contractNoFormat = contractNo.trim();

    // 先下载到 daoQiRiDir 目录
    const downloadSuccess = await downloadPDFFile(
      fileLocator,
      page,
      dirs.daoQiRiDir,
      `到期日调整申请书-${contractNoFormat}`,
    );

    // 如果下载成功，复制文件到 rongZiDaoQiRiDir 目录并重命名
    if (downloadSuccess) {
      const sourceFilePath = path.join(
        dirs.daoQiRiDir,
        `到期日调整申请书-${contractNoFormat}.pdf`,
      );
      const targetFilePath = path.join(
        dirs.rongZiDaoQiRiDir,
        `融资到期日调整申请书-${contractNoFormat}.pdf`,
      );

      fs.copyFileSync(sourceFilePath, targetFilePath);
    }
  } catch (error) {
    logger.error(`下载失败: ${error.message}`);
  }
}

// 反转让
async function getFanZhuanRang(page, detailButton, dirs) {
  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await page.waitForTimeout(3000);

    const fileLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a';

    const contractNoLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[3]/div';
    const fileElement = page.locator(fileLocator);

    const elementCount = await fileElement.count();

    if (elementCount === 0) {
      return;
    }
    const contractNo = await page.locator(contractNoLocator).textContent();
    const contractNoFormat = contractNo.trim();
    await downloadPDFFile(
      fileLocator,
      page,
      dirs.fanZhuanRangDir,
      `应收帐款反转让通知书-${contractNoFormat}`,
    );
  } catch (error) {
    logger.error(`下载失败: ${error.message}`);
  }
}

// 账款跟踪
async function getZKGZDetail(page, detailButton, dirs) {
  logger.info('干嘛啊');
  await detailButton.click();
  await page.waitForLoadState('networkidle', {
    timeout: 15000,
  });
  await page.waitForTimeout(3000);

  const fileLocator =
    'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div[3]/div/div/div/div/div[3]/table/tbody/tr/td[3]/div/div/a';

  const contractNoLocator =
    'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[3]/div';

  const fileElement = page.locator(fileLocator);
  const noElement = page.locator(contractNoLocator);

  const elementCount = await fileElement.count();
  const noCount = await noElement.count();

  logger.info(`==== ${elementCount} 条`);

  if (elementCount === 0 || noCount === 0) {
    return;
  }
  const contractNo = await noElement.textContent();
  const contractNoFormat = contractNo.trim();
  await downloadPDFFile(
    fileLocator,
    page,
    dirs.zhangKuanGenZongDir,
    `应收帐款转让通知函-${contractNoFormat}`,
  );

  // /html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div[3]/div/div/div/div/div[3]/table/tbody/tr/td[3]/div/div/a
}

// 提取查询逻辑为独立方法
async function performDaoQiRiSearch(page, customerName, type) {
  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.waitForTimeout(3000);

  // /html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div

  const locatorMap = {
    dqr: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[3]/div/div/div[1]',
    fzr: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div[1]',
    zkgz: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div[1]',
  };

  const locator = locatorMap[type];
  const customerNameSearchInput = page.locator(locator);

  await customerNameSearchInput.click();
  const inputDom = customerNameSearchInput.locator('input');
  await inputDom.fill(customerName);
  await inputDom.press('Enter');

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.getByRole('listitem').filter({ hasText: customerName }).click();

  await page.getByRole('button', { name: '查询' }).click();
  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.waitForTimeout(3000);
}

/* 
  @param type: dqr = 到期日调整 fzr = 反转让通知
*/
async function getZiChanDaoQi(page, type, customerName, dirInfo) {
  const typeNameMap = {
    dqr: '资产到期日调整',
    fzr: '反转让调整',
    zkgz: '帐款跟踪',
  };
  logger.info(`开始获取${typeNameMap[type]}`);

  try {
    const targetUrlMap = {
      dqr: 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/dueDateAdjust',
      fzr: 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/atAccount',
      zkgz: 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/assetsTracking',
    };
    await tabGoPage(page, targetUrlMap[type]);

    // 执行查询逻辑
    await performDaoQiRiSearch(page, customerName, type);

    // 查找表格中的详情按钮
    const detailButtons = page.getByRole('button', { name: '详情' });
    const count = await detailButtons.count();

    logger.info(`找到了 ${count} 条`);

    for (let i = 0; i < count; i++) {
      // 实测不需要重新查询，返回列表页时，查询结果有保留
      // 重新执行查询以确保页面状态正确
      // if (i > 0) {
      //   await performDaoQiRiSearch(page, customerName, type);
      // }
      // 重新获取按钮并点击第i个
      // const currentButtons = page.getByRole('button', {
      //   name: '详情',
      // });
      const btn = await detailButtons.nth(i);
      if (type === 'dqr') {
        await getZiChanDaoQiDetail(page, btn, dirInfo);
      } else if (type === 'fzr') {
        await getFanZhuanRang(page, btn, dirInfo);
      } else if (type === 'zkgz') {
        await getZKGZDetail(page, btn, dirInfo);
      }
      await goBack(page);
    }
    logger.success(`${typeNameMap[type]}收集成功`);
  } catch (error) {
    console.error(error.message);
    logger.error(`获取到期日调整通知书失败： ${error.message}`);
  }
}

module.exports = {
  getZiChanDaoQi,
};
