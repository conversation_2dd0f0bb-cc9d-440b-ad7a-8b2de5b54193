const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadPDFFile } = require('../utils/pdfDownloader');

async function getZiChanDaoQiDetail(page, detailButton, dirs) {
  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });

    const fileLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a';

    const contractNoLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[3]/div';
    const fileElement = page.locator(fileLocator);
    await page.waitForTimeout(3000);
    const elementCount = await fileElement.count();

    if (elementCount === 0) {
      return;
    }
    // const fileName = await detailPage.locator(fileLocator).textContent();
    const contractNo = await page.locator(contractNoLocator).textContent();
    const contractNoFormat = contractNo.trim();
    await downloadPDFFile(
      fileLocator,
      page,
      dirs.daoQiRiDir,
      `到期日调整申请书-${contractNoFormat}`,
    );

    await downloadPDFFile(
      fileLocator,
      page,
      dirs.rongZiDaoQiRiDir,
      `融资到期日调整申请书-${contractNoFormat}`,
    );
  } catch (error) {
    logger.error(`下载失败: ${error.message}`);
  }
}

async function getFanZhuanRang(page, detailButton, dirs) {
  logger.info('收集反转让');

  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });

    const fileLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a';

    const contractNoLocator =
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[3]/div';
    const fileElement = page.locator(fileLocator);
    await page.waitForTimeout(3000);
    const elementCount = await fileElement.count();

    if (elementCount === 0) {
      return;
    }
    const contractNo = await page.locator(contractNoLocator).textContent();
    const contractNoFormat = contractNo.trim();
    await downloadPDFFile(
      fileLocator,
      page,
      dirs.fanZhuanRangDir,
      `应收帐款反转让通知书-${contractNoFormat}`,
    );
  } catch (error) {
    logger.error(`下载失败: ${error.message}`);
  }
}

// 提取查询逻辑为独立方法
async function performDaoQiRiSearch(page, customerName, type) {
  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.waitForTimeout(3000);

  const locator =
    type === 'dqr'
      ? 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[3]/div/div/div[1]'
      : 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div[1]';
  const customerNameSearchInput = page.locator(locator);

  await customerNameSearchInput.click();
  const inputDom = customerNameSearchInput.locator('input');
  await inputDom.fill(customerName);
  await inputDom.press('Enter');

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.getByRole('listitem').filter({ hasText: customerName }).click();

  await page.getByRole('button', { name: '查询' }).click();
  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.waitForTimeout(3000);
}

/* 
  @param type: dqr = 到期日调整 fzr = 反转让通知
*/
async function getZiChanDaoQi(page, type, customerName, dirInfo) {
  const typeName = type === 'dqr' ? '资产到期日调整' : '反转让调整';
  logger.info(`开始获取${typeName}`);

  try {
    const targetUrl =
      type === 'dqr'
        ? 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/dueDateAdjust'
        : 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/atAccount';
    await tabGoPage(page, targetUrl);

    // 执行查询逻辑
    await performDaoQiRiSearch(page, customerName, type);

    // 查找表格中的详情按钮
    const detailButtons = page.getByRole('button', { name: '详情' });
    const count = await detailButtons.count();

    for (let i = 0; i < count; i++) {
      // 实测不需要重新查询，返回列表页时，查询结果有保留
      // 重新执行查询以确保页面状态正确
      // if (i > 0) {
      //   await performDaoQiRiSearch(page, customerName, type);
      // }
      // 重新获取按钮并点击第i个
      // const currentButtons = page.getByRole('button', {
      //   name: '详情',
      // });
      const btn = await detailButtons.nth(i);
      if (type === 'dqr') {
        await getZiChanDaoQiDetail(page, btn, dirInfo);
      } else if (type === 'fzr') {
        await getFanZhuanRang(page, btn, dirInfo);
      }
      await goBack(page);
    }
    logger.success(`${typeName}收集成功`);
  } catch (error) {
    console.error(error.message);
    logger.error(`获取到期日调整通知书失败： ${error.message}`);
  }
}

module.exports = {
  getZiChanDaoQi,
};
