const { createBrowser, createPage, closeBrowser } = require('../utils/browser');
const logger = require('../utils/logger');
const config = require('../utils/config');
const RecordManager = require('../utils/record');
const path = require('path');
const fs = require('fs');

/**
 * 示例脚本 - 演示如何使用框架进行网页自动化
 *
 * 这个脚本演示了：
 * 1. 如何读取配置
 * 2. 如何使用记录管理器避免重复处理
 * 3. 如何进行基本的网页操作
 * 4. 如何保存截图
 * 5. 错误处理
 */
async function run() {
  const configInstance = config.getInstance();
  const records = new RecordManager('example');

  let browser = null;
  let page = null;

  try {
    // 步骤1：初始化浏览器
    logger.step('初始化浏览器', 1);
    browser = await createBrowser();
    page = await createPage(browser);

    // 步骤2：访问目标网页
    logger.step('访问目标网页', 2);
    const targetUrl =
      configInstance.get('customSettings.url') || 'https://example.com';
    await page.goto(targetUrl, { waitUntil: 'networkidle' });
    logger.info('页面加载成功', { url: targetUrl });

    // 步骤3：获取表格数据
    logger.step('获取表格数据', 3);

    // 定义选择器常量，便于维护
    const SELECTORS = {
      table: '.the-table-component .el-table',
      tableBody: '.el-table__body-wrapper tbody',
      tableRow: 'tr.el-table__row',
      // 提款申请号列（第3列）
      applicationNumberCell: '.el-table_1_column_4 .cell',
      // 业务状态列（倒数第二列）
      businessStatusCell: '.el-table_1_column_21 .cell',
      link: 'a',
    };

    // 等待表格加载完成
    logger.info('等待表格加载...');
    try {
      // 使用更智能的等待策略
      await page.waitForSelector(SELECTORS.table, {
        state: 'visible',
        timeout: 10000,
      });

      // 额外等待数据加载（如果表格是动态加载的）
      await page.waitForFunction(
        (selector) => {
          const table = document.querySelector(selector);
          return table && table.querySelectorAll('.el-table__row').length > 0;
        },
        SELECTORS.table,
        { timeout: 5000 },
      );
    } catch (error) {
      logger.error('表格未找到或加载超时', error);

      // 保存当前页面状态供调试
      const debugScreenshot = path.join(
        __dirname,
        '../../screenshots',
        `debug_table_not_found_${Date.now()}.png`,
      );
      await page.screenshot({ path: debugScreenshot, fullPage: true });
      logger.error('已保存调试截图', { path: debugScreenshot });

      throw new Error('无法找到目标表格，请检查选择器或页面是否正确加载');
    }

    // 使用 Playwright 原生 API 提取数据
    logger.info('开始提取表格数据...');
    const tableData = [];

    // 获取所有表格行
    const rows = await page
      .locator(
        `${SELECTORS.table} ${SELECTORS.tableBody} ${SELECTORS.tableRow}`,
      )
      .all();
    logger.info(`找到 ${rows.length} 行数据`);

    // 遍历每一行
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];

      try {
        // 获取业务状态
        const statusText = await row
          .locator(SELECTORS.businessStatusCell)
          .textContent();
        const status = statusText?.trim() || '';

        // 只处理状态为"已完成"的行
        if (status === '已完成') {
          // 获取提款申请号
          let applicationNumber = '';
          let href = '';

          // 尝试获取链接
          const link = row
            .locator(`${SELECTORS.applicationNumberCell} ${SELECTORS.link}`)
            .first();
          const linkCount = await link.count();

          if (linkCount > 0) {
            applicationNumber = await link.textContent();
            applicationNumber = applicationNumber?.trim() || '';
            // 同时获取链接地址，可能后续处理需要
            href = (await link.getAttribute('href')) || '';
          } else {
            // 如果没有链接，尝试获取单元格文本
            applicationNumber = await row
              .locator(SELECTORS.applicationNumberCell)
              .textContent();
            applicationNumber = applicationNumber?.trim() || '';
          }

          // 如果成功获取提款申请号，添加到结果
          if (applicationNumber) {
            const rowData = {
              id: applicationNumber,
              title: applicationNumber,
              status: status,
              rowIndex: i + 1,
              href: href,
            };

            tableData.push(rowData);
            logger.debug(`提取第 ${i + 1} 行数据`, {
              applicationNumber,
              status,
            });
          }
        }
      } catch (error) {
        logger.warn(`处理第 ${i + 1} 行时出错`, error);
        // 继续处理下一行
      }
    }

    logger.data('获取到', tableData.length, {
      source: '页面表格',
      totalCompleted: tableData.length,
    });

    // 如果没有找到任何已完成的项目
    if (tableData.length === 0) {
      logger.warn('未找到任何已完成的项目');
      return; // 提前结束
    }

    // 步骤4：点击详情按钮访问详情页面
    logger.step('点击详情按钮访问详情页面', 4);

    for (let i = 0; i < tableData.length; i++) {
      const item = tableData[i];
      logger.info(`正在处理第 ${i + 1}/${tableData.length} 个项目`, {
        applicationNumber: item.title,
        status: item.status,
      });

      try {
        // 点击对应行的详情按钮
        await clickDetailButton(page, item.rowIndex, item.title);

        // 等待详情页面加载
        await page.waitForTimeout(2000);

        // 这里可以添加详情页面的处理逻辑
        logger.info(`成功访问 ${item.title} 的详情页面`);

        // 返回到列表页面（如果需要）
        await page.goBack();
        await page.waitForTimeout(1000);
      } catch (error) {
        logger.error(`处理 ${item.title} 时出错`, error);
        // 继续处理下一个项目
      }
    }

    logger.success('所有详情页面访问完成');
  } finally {
    // 清理资源
    await closeBrowser(browser);
  }
}

// 导出 run 函数
module.exports = { run };

// 如果直接运行此文件或通过运行器启动
if (require.main === module || process.env.SCRIPT_RUNNER === 'true') {
  run().catch((error) => {
    logger.error('脚本运行失败', error);
    process.exit(1);
  });
}
