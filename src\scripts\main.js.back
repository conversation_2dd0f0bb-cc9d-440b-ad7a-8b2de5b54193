const { createBrowser, createPage, closeBrowser } = require('../utils/browser');
const logger = require('../utils/logger');
const config = require('../utils/config');
const { downloadPDFFile } = require('../utils/pdfDownloader');
const inquirer = require('inquirer');
const fs = require('fs');
const path = require('path');

// 简单、可靠、高效的解决方案
async function clickDetailButtonPractical(page, withdrawalNumber) {
  try {
    // 1. 等待查询结果
    await page.waitForFunction(
      (targetNumber) => {
        const rows = document.querySelectorAll(
          '.el-table__body-wrapper tr.el-table__row',
        );
        return (
          rows.length === 1 && // 确保只有一行结果
          Array.from(rows).some((row) => row.textContent.includes(targetNumber))
        );
      },
      withdrawalNumber,
      { timeout: 15000 },
    );

    // 2. 定位详情按钮（使用最稳定的选择器）
    const detailButton = page
      .locator('.el-table__fixed-right .el-table__fixed-body-wrapper')
      .locator('button:has-text("详情")')
      .first();

    // 3. 确保按钮可交互
    await detailButton.waitFor({ state: 'visible', timeout: 5000 });

    // 4. 点击按钮
    await detailButton.click();

    logger.success('成功点击详情按钮');

    // 5. 等待详情页面加载
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    logger.info('详情页面已加载');

    // 等待接口请求稳定
    logger.info('⏳ 等待接口请求稳定...');
    await page.waitForLoadState('networkidle', { timeout: 15000 });

    // 额外等待确保所有异步请求完成
    await page.waitForTimeout(1000);
    logger.info('接口请求已稳定，开始查找元素');

    // 6. 查找指定xPath的元素并获取文本内容
    try {
      const targetElement = page.locator(
        'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div/form/div/div/div[2]/div/div/div/div/a/span',
      );
      await targetElement.waitFor({ state: 'visible', timeout: 10000 });

      const elementText = await targetElement.textContent();
      logger.success(`成功获取元素文本内容: "${elementText}"`);

      // 创建以元素文本内容命名的目录
      try {
        const configInstance = config.getInstance();
        const archiveDir = configInstance.get('customSettings.archiveDir');

        if (archiveDir && elementText) {
          // 清理文件名，移除不合法的字符
          const sanitizedName = elementText
            .replace(/[<>:"/\\|?*]/g, '_')
            .trim();
          const targetDir = path.join(archiveDir, sanitizedName);

          // 检查目录是否已存在
          if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
            logger.success(`成功创建目录: "${targetDir}"`);
          } else {
            logger.info(`📁 目录已存在: "${targetDir}"`);
          }

          // 在该目录下创建"宁波国富商业保理有限公司"子目录
          const companyDir = path.join(targetDir, '宁波国富商业保理有限公司');
          if (!fs.existsSync(companyDir)) {
            fs.mkdirSync(companyDir, { recursive: true });
            logger.success(`成功创建公司目录: "${companyDir}"`);
          } else {
            logger.info(`📁 公司目录已存在: "${companyDir}"`);
          }

          // 获取第二个xpath元素的文本内容
          try {
            const secondElement = page.locator(
              'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[9]/div/div/form[2]/div/div[3]/table/tbody/tr/td[8]/div/span',
            );
            await secondElement.waitFor({ state: 'visible', timeout: 10000 });

            const secondElementText = await secondElement.textContent();
            logger.success(
              `成功获取第二个元素文本内容: "${secondElementText}"`,
            );

            if (secondElementText && secondElementText.length >= 8) {
              // 取前7个字符作为目录名
              const shortName = secondElementText.substring(0, 8);
              const sanitizedShortName = shortName
                .replace(/[<>:"/\\|?*]/g, '_')
                .trim();
              const finalDir = path.join(companyDir, sanitizedShortName);

              if (!fs.existsSync(finalDir)) {
                fs.mkdirSync(finalDir, { recursive: true });
                logger.success(`成功创建最终目录: "${finalDir}"`);
              } else {
                logger.info(`📁 最终目录已存在: "${finalDir}"`);
              }

              // 在finalDir下创建"提款文件"和"转让文件"两个文件夹
              const withdrawalDir = path.join(finalDir, '提款文件');
              const transferDir = path.join(finalDir, '转让文件');

              if (!fs.existsSync(withdrawalDir)) {
                fs.mkdirSync(withdrawalDir, { recursive: true });
                logger.success(`成功创建提款文件目录: "${withdrawalDir}"`);
              } else {
                logger.info(`提款文件目录已存在: "${withdrawalDir}"`);
              }

              if (!fs.existsSync(transferDir)) {
                fs.mkdirSync(transferDir, { recursive: true });
                logger.success(`成功创建转让文件目录: "${transferDir}"`);
              } else {
                logger.info(`转让文件目录已存在: "${transferDir}"`);
              }

              // 获取提款流程编号并在提款文件夹下创建对应目录
              try {
                const withdrawalNumberElement = page.locator(
                  'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div/form/div/div/div[1]/div/div/span',
                );
                await withdrawalNumberElement.waitFor({
                  state: 'visible',
                  timeout: 10000,
                });

                const withdrawalNumber =
                  await withdrawalNumberElement.textContent();
                logger.success(`成功获取提款流程编号: "${withdrawalNumber}"`);

                if (withdrawalNumber && withdrawalNumber.trim()) {
                  const sanitizedWithdrawalNumber = withdrawalNumber
                    .trim()
                    .replace(/[<>:"/\\|?*]/g, '_');
                  const withdrawalNumberDir = path.join(
                    withdrawalDir,
                    sanitizedWithdrawalNumber,
                  );

                  if (!fs.existsSync(withdrawalNumberDir)) {
                    fs.mkdirSync(withdrawalNumberDir, { recursive: true });
                    logger.success(
                      `成功创建提款流程编号目录: "${withdrawalNumberDir}"`,
                    );
                  } else {
                    logger.info(
                      `提款流程编号目录已存在: "${withdrawalNumberDir}"`,
                    );
                  }

                  // 下载融资申请书文件
                  try {
                    const downloadLinkElement = page.locator(
                      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[13]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a',
                    );
                    await downloadLinkElement.waitFor({
                      state: 'visible',
                      timeout: 10000,
                    });

                    logger.success('成功找到融资申请书下载链接');

                    // 使用封装的PDF下载工具函数
                    const fileName = `融资申请书`;
                    const downloadSuccess = await downloadPDFFile(
                      downloadLinkElement,
                      page,
                      withdrawalNumberDir,
                      fileName,
                    );

                    if (downloadSuccess) {
                      logger.success('融资申请书下载完成');
                    } else {
                      logger.warn('融资申请书下载失败');
                    }
                  } catch (downloadError) {
                    logger.warn(
                      `下载融资申请书文件失败: ${downloadError.message}`,
                    );
                  }
                } else {
                  logger.warn('提款流程编号为空，跳过创建提款流程编号目录');
                }
              } catch (withdrawalNumberError) {
                logger.warn(
                  `未能找到或获取提款流程编号: ${withdrawalNumberError.message}`,
                );
              }
            } else {
              logger.warn('第二个元素文本内容为空或长度不足7个字符');
            }
          } catch (secondElementError) {
            logger.warn(
              `未能找到或获取第二个元素的文本内容: ${secondElementError.message}`,
            );
          }
        } else {
          logger.warn('配置中未找到archiveDir或元素文本为空，跳过目录创建');
        }
      } catch (dirError) {
        logger.error(`创建目录失败: ${dirError.message}`);
      }
    } catch (error) {
      logger.warn(`未能找到或获取指定元素的文本内容: ${error.message}`);

      // 尝试截图以便调试
      try {
        const screenshotPath = `screenshots/detail_page_${withdrawalNumber}_${Date.now()}.png`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        logger.info(`已保存详情页面截图: ${screenshotPath}`);
      } catch (screenshotError) {
        logger.warn('保存截图失败', screenshotError);
      }
    }

    // 7. 返回到列表页面
    await page.goBack();
    await page.waitForTimeout(1000);
    logger.info('已返回到列表页面');
  } catch (error) {
    // 5. 失败时的调试信息
    const buttonCount = await page.locator('.el-button').count();
    const rowCount = await page
      .locator('.el-table__body-wrapper tr.el-table__row')
      .count();

    logger.error(`点击失败: ${error.message}`, {
      buttonCount,
      rowCount,
      withdrawalNumber,
    });

    throw error;
  }
}

/**
 * 用户确认关闭浏览器
 * @returns {Promise<void>}
 */
async function confirmBrowserClose() {
  logger.separator('═', 60, 'blue');
  logger.info('🎯 查询任务已完成');

  const questions = [
    {
      type: 'confirm',
      name: 'closeBrowser',
      message: '是否关闭浏览器?',
      prefix: '🔒',
      default: true,
      suffix: ' (选择 No 可以保持浏览器打开以便手动操作)',
    },
  ];

  const { closeBrowser } = await inquirer.prompt(questions);

  if (closeBrowser) {
    logger.info('用户确认关闭浏览器');
  } else {
    logger.info('用户选择保持浏览器打开');
    logger.warn(' 浏览器将保持打开状态，请手动关闭');

    // 显示一些有用的信息
    logger.table(
      [
        '查询任务已完成',
        '🌐 浏览器保持打开状态',
        '👆 您可以继续手动操作页面',
        '🔒 完成后请手动关闭浏览器窗口',
      ],
      '温馨提示',
    );

    // 等待用户按任意键继续
    const continueQuestion = [
      {
        type: 'input',
        name: 'continue',
        message: '按 Enter 键退出脚本 (浏览器将保持打开)',
        prefix: '⏸️',
      },
    ];

    await inquirer.prompt(continueQuestion);
    logger.info('脚本退出，浏览器保持打开状态');

    // 设置标志，告诉finally块不要关闭浏览器
    global.keepBrowserOpen = true;
  }

  logger.separator('═', 60, 'blue');
}

/**
 * 错误情况下用户确认关闭浏览器
 * @returns {Promise<void>}
 */
async function confirmBrowserCloseOnError() {
  logger.separator('═', 60, 'red');
  logger.warn('脚本执行过程中出现错误');

  const questions = [
    {
      type: 'confirm',
      name: 'closeBrowser',
      message: '是否关闭浏览器?',
      prefix: '🔒',
      default: false, // 出错时默认不关闭，让用户可以查看页面状态
      suffix: ' (选择 No 可以保持浏览器打开以便调试问题)',
    },
  ];

  const { closeBrowser } = await inquirer.prompt(questions);

  if (closeBrowser) {
    logger.info('用户确认关闭浏览器');
  } else {
    logger.info('用户选择保持浏览器打开以便调试');
    logger.warn(' 浏览器将保持打开状态，您可以检查页面状态');

    // 显示调试提示
    logger.table(
      [
        '脚本执行出现错误',
        '🌐 浏览器保持打开状态',
        '🔍 您可以检查页面状态进行调试',
        '📸 错误截图已保存',
        '🔒 调试完成后请手动关闭浏览器窗口',
      ],
      '调试提示',
    );

    // 设置标志，告诉finally块不要关闭浏览器
    global.keepBrowserOpen = true;
  }

  logger.separator('═', 60, 'red');
}

/**
 * 获取用户输入的提款申请号
 * @returns {Promise<string>} 用户输入的字符串
 */
async function getUserInput() {
  logger.separator('─', 60, 'cyan');
  logger.info('📝 准备输入提款申请号');

  const questions = [
    {
      type: 'input',
      name: 'withdrawalNumbers',
      message: '请输入需要查询的提款申请号:',
      prefix: '🔍',
      suffix: ' (多个号码用空格分隔)',
      validate: (input) => {
        const trimmed = input.trim();
        if (!trimmed) {
          return '请输入至少一个提款申请号';
        }

        return true;
      },
      filter: (input) => input.trim(),
    },
  ];

  const answers = await inquirer.prompt(questions);
  return answers.withdrawalNumbers;
}

/**
 * 解析和验证提款申请号
 * @param {string} input - 用户输入的字符串
 * @returns {Array<string>} 有效的提款申请号数组
 */
function parseWithdrawalNumbers(input) {
  if (!input) {
    return [];
  }

  // 按空格分割，过滤空字符串，去重
  const numbers = input
    .split(/\s+/)
    .filter((num) => num.trim().length > 0)
    .map((num) => num.trim());

  // 去重
  const uniqueNumbers = [...new Set(numbers)];
  const duplicateCount = numbers.length - uniqueNumbers.length;

  // 显示解析结果
  logger.success(`成功解析 ${uniqueNumbers.length} 个提款申请号`);

  if (duplicateCount > 0) {
    logger.warn(`已自动去除 ${duplicateCount} 个重复的申请号`);
  }

  // 使用表格显示申请号列表
  if (uniqueNumbers.length <= 10) {
    logger.table(
      uniqueNumbers.map((num, index) => `${index + 1}. ${num}`),
      '待查询的提款申请号',
    );
  } else {
    logger.table(
      [
        ...uniqueNumbers
          .slice(0, 5)
          .map((num, index) => `${index + 1}. ${num}`),
        `... 还有 ${uniqueNumbers.length - 5} 个申请号`,
      ],
      '待查询的提款申请号（显示前5个）',
    );
  }

  return uniqueNumbers;
}

/**
 * 查询单个提款申请号
 * @param {Page} page - Playwright 页面对象
 * @param {string} withdrawalNumber - 提款申请号
 * @returns {Promise<boolean>} 查询是否成功
 */
async function searchSingleWithdrawal(page, withdrawalNumber) {
  try {
    logger.info(`开始查询提款申请号: ${withdrawalNumber}`);

    // 定位输入框
    const inputLocator = page.locator('input[placeholder="请输入提款申请号"]');
    await inputLocator.waitFor({ state: 'visible', timeout: 10000 });

    // 清空并输入提款申请号
    await inputLocator.clear();
    await inputLocator.fill(withdrawalNumber);
    logger.warn(`已输入提款申请号: ${withdrawalNumber}`);

    // 定位并点击查询按钮
    const searchButtonLocator = page.locator(
      '.search-btns .el-button.el-button--info.el-button--mini',
    );
    await searchButtonLocator.waitFor({ state: 'visible', timeout: 5000 });
    await searchButtonLocator.click();
    logger.warn('已点击查询按钮');

    // 等待查询结果加载
    await page.waitForLoadState('networkidle', { timeout: 15000 });

    // 新增：点击详情按钮访问详情页面
    await clickDetailButtonPractical(page, withdrawalNumber);

    return true;
  } catch (error) {
    logger.error(`查询提款申请号 ${withdrawalNumber} 失败`, error);
    return false;
  }
}

/**
 * 处理多个提款申请号
 * @param {Page} page - Playwright 页面对象
 * @param {Array<string>} numbers - 提款申请号数组
 */
async function processWithdrawalNumbers(page, numbers) {
  if (numbers.length === 0) {
    logger.warn('没有有效的提款申请号需要处理');
    return;
  }

  // 用户确认是否开始查询
  logger.separator('─', 60, 'yellow');

  logger.step(`开始处理 ${numbers.length} 个提款申请号`, 10);

  const results = {
    total: numbers.length,
    success: 0,
    failed: 0,
    failedNumbers: [],
    startTime: Date.now(),
  };

  for (let i = 0; i < numbers.length; i++) {
    const number = numbers[i];

    // 显示进度条
    logger.progress(i + 1, numbers.length, `查询 ${number}`);

    const success = await searchSingleWithdrawal(page, number);

    if (!success) {
      results.success++;
      logger.error(`${number} 查询失败`);
    }

    // 在查询之间添加延迟，避免过于频繁的请求
    if (i < numbers.length - 1) {
      logger.startSpinner('等待 2 秒后继续下一个查询...');
      await page.waitForTimeout(2000);
      logger.stopSpinner();
    }
  }

  // 计算总耗时
  const duration = Date.now() - results.startTime;
  const avgTime = Math.round(duration / results.total);

  // 输出处理结果统计
  logger.separator('═', 60, 'green');
  logger.success('所有提款申请号处理完成');

  logger.table(
    {
      总数: results.total,
      成功: `${results.success} (${Math.round(
        (results.success / results.total) * 100,
      )}%)`,
      失败: `${results.failed} (${Math.round(
        (results.failed / results.total) * 100,
      )}%)`,
      总耗时: `${Math.round(duration / 1000)}秒`,
      平均耗时: `${avgTime}ms/个`,
    },
    '查询结果统计',
  );

  if (results.failed > 0) {
    logger.table(
      results.failedNumbers.map((num, index) => `${index + 1}. ${num}`),
      '查询失败的申请号',
    );
  }

  logger.separator('═', 60, 'green');
}

/**
 * 确保页面跳转到目标地址
 * @param {Page} page - Playwright 页面对象
 * @param {string} targetUrl - 目标URL
 * @param {number} maxRetries - 最大重试次数
 */
async function ensureTargetPage(page, targetUrl, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    const currentUrl = page.url();

    if (currentUrl.includes('/operation-web/drawManage')) {
      logger.success('已在目标页面', { url: currentUrl });
      return true;
    }

    logger.info(`尝试跳转到目标页面 (${i + 1}/${maxRetries})`, { targetUrl });

    try {
      await page.goto(targetUrl, {
        waitUntil: 'networkidle',
        timeout: 30000,
      });

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      const newUrl = page.url();
      if (newUrl.includes('/operation-web/drawManage')) {
        logger.success('跳转成功', { url: newUrl });
        return true;
      }
    } catch (error) {
      logger.warn(`第 ${i + 1} 次跳转失败`, error.message);
      if (i === maxRetries - 1) {
        throw error;
      }
      // 等待一下再重试
      await page.waitForTimeout(2000);
    }
  }

  return false;
}

async function run() {
  const configInstance = config.getInstance();
  let browser = null;
  let page = null;

  try {
    // 1. 初始化浏览器
    logger.step('初始化浏览器', 1);
    browser = await createBrowser();
    page = await createPage(browser);

    // 2. 构建登录URL（带目标页面参数）
    logger.step('访问登录页面', 2);
    const targetUrl =
      'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/operation-web/drawManage';
    const encodedTargetUrl = encodeURIComponent(targetUrl);
    const loginUrl = `https://gfconsole-wsuat.syitservice.com/common/#/login?url=${encodedTargetUrl}`;

    await page.goto(loginUrl, { waitUntil: 'networkidle' });
    logger.info('登录页面加载成功', { url: loginUrl });

    // 3. 等待登录表单加载
    logger.step('等待登录表单加载', 3);
    await page.waitForSelector('#username', { timeout: 10000 });
    await page.waitForSelector('#password', { timeout: 10000 });
    logger.info('登录表单加载完成');

    // 4. 填写登录信息
    logger.step('填写登录信息', 4);
    const account = configInstance.get('customSettings.account');
    const password = configInstance.get('customSettings.userPassword');

    if (!account || !password) {
      throw new Error(
        '配置中缺少登录凭据，请检查 customSettings.account 和 customSettings.userPassword',
      );
    }

    // 清空并填写用户名
    await page.fill('#username', '');
    await page.fill('#username', account);
    logger.info('已填写用户名', { account });

    // 清空并填写密码
    await page.fill('#password', '');
    await page.fill('#password', password);
    logger.info('已填写密码');

    // 5. 点击登录按钮
    logger.step('点击登录按钮', 5);
    await page.click('button.large.block.login-btn');
    logger.info('已点击登录按钮');

    // 6. 等待登录成功
    logger.step('等待登录成功', 6);
    logger.info('正在等待登录成功...（如果出现验证码，请在1分钟内手动输入）');

    // 等待登录完成（不一定要跳转到目标页面）
    const maxWaitTime = 60000;
    const checkInterval = 1000;
    const startTime = Date.now();
    let loginSuccess = false;

    while (Date.now() - startTime < maxWaitTime) {
      const currentUrl = page.url();

      // 如果不在登录页面，说明登录成功
      if (!currentUrl.includes('/login')) {
        logger.info('检测到登录成功');
        loginSuccess = true;
        break;
      }

      await page.waitForTimeout(checkInterval);

      // 显示进度
      const elapsed = Math.floor((Date.now() - startTime) / 1000);
      if (elapsed % 15 === 0 && elapsed > 0) {
        logger.info(`等待登录中... (${elapsed}/60秒)`);
      }
    }

    if (!loginSuccess) {
      const currentUrl = page.url();
      if (!currentUrl.includes('/login')) {
        logger.info('登录成功，但检测超时');
      } else {
        logger.error('登录失败，仍在登录页面', { currentUrl });
        throw new Error('登录失败或超时');
      }
    }

    logger.success('登录成功');

    // 7. 确保跳转到目标页面
    logger.step('确保跳转到目标页面', 7);
    const success = await ensureTargetPage(page, targetUrl);

    if (!success) {
      throw new Error('无法跳转到目标页面');
    }

    logger.success('已成功跳转到目标页面，准备开始查询流程');

    // 8. 获取用户输入的提款申请号
    // logger.step('获取用户输入', 8);
    // const userInput = await getUserInput();

    // if (!userInput) {
    //   logger.warn('用户未输入任何提款申请号，脚本结束');
    //   return;
    // }

    // 9. 解析提款申请号
    logger.step('解析提款申请号', 9);
    const withdrawalNumbers = parseWithdrawalNumbers('LR20250425000005'); // userInput

    if (withdrawalNumbers.length === 0) {
      logger.warn('没有有效的提款申请号，脚本结束');
      return;
    }

    // 10. 处理提款申请号查询
    await processWithdrawalNumbers(page, withdrawalNumbers);

    // 11. 获取客户名称
    //*[@id="krBU7P3GDQ"]/div/div[2]/div/div/div/div/a/span

    logger.success('所有查询任务执行完成');

    // 11. 用户确认关闭浏览器
    await confirmBrowserClose();
  } catch (error) {
    logger.error('脚本执行失败', error);

    // 保存错误截图
    if (page) {
      try {
        const errorScreenshotPath = `screenshots/error_${Date.now()}.png`;
        await page.screenshot({ path: errorScreenshotPath, fullPage: true });
        logger.info('已保存错误截图', { path: errorScreenshotPath });
      } catch (screenshotError) {
        logger.error('保存错误截图失败', screenshotError);
      }
    }

    // 即使出错也询问用户是否关闭浏览器
    try {
      await confirmBrowserCloseOnError();
    } catch (confirmError) {
      logger.warn('用户确认过程出错，将自动关闭浏览器', confirmError);
    }

    throw error;
  } finally {
    // 只有在用户确认关闭或出现错误时才关闭浏览器
    if (!global.keepBrowserOpen) {
      await closeBrowser(browser);
    } else {
      logger.info('根据用户选择，浏览器保持打开状态');
    }
  }
}

module.exports = { run };

if (require.main === module || process.env.SCRIPT_RUNNER === 'true') {
  run().catch((error) => {
    logger.error('脚本运行失败', error);
    process.exit(1);
  });
}
