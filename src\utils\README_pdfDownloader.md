# PDF下载工具使用指南

## 概述

`pdfDownloader.js` 是一个专门用于处理PDF文件下载的工具模块，它可以拦截PDF预览页面并自动将其转换为文件下载。

## 主要功能

- 自动拦截PDF预览页面
- 将PDF预览转换为文件下载
- 支持自定义文件名和下载路径
- 提供完整的错误处理和日志记录
- 单例模式，避免重复监听

## 使用方法

### 1. 导入模块

```javascript
const { downloadPDFFile } = require('../utils/pdfDownloader');
```

### 2. 基本使用

```javascript
// 假设你已经找到了下载链接元素
const downloadLinkElement = page.locator('a[href*=".pdf"]');

// 下载PDF文件
const downloadSuccess = await downloadPDFFile(
  downloadLinkElement,  // 下载链接元素
  page,                // Playwright页面对象
  '/path/to/download',  // 下载目录
  'my_document'        // 文件名（不包含.pdf扩展名）
);

if (downloadSuccess) {
  console.log('下载成功');
} else {
  console.log('下载失败');
}
```

### 3. 完整示例

```javascript
const { downloadPDFFile } = require('../utils/pdfDownloader');
const path = require('path');

async function downloadFinancingApplication(page, withdrawalNumber, downloadDir) {
  try {
    // 查找下载链接
    const downloadLinkElement = page.locator(
      'xpath=//a[contains(@href, ".pdf")]'
    );
    
    await downloadLinkElement.waitFor({
      state: 'visible',
      timeout: 10000,
    });
    
    // 使用PDF下载工具
    const fileName = `融资申请书_${withdrawalNumber}`;
    const downloadSuccess = await downloadPDFFile(
      downloadLinkElement,
      page,
      downloadDir,
      fileName,
      5000  // 可选：等待时间（毫秒）
    );
    
    return downloadSuccess;
  } catch (error) {
    console.error('下载失败:', error.message);
    return false;
  }
}
```

## API 参考

### downloadPDFFile(downloadLinkElement, page, downloadDir, fileName, waitTime)

**参数：**
- `downloadLinkElement` (Locator): Playwright定位器对象，指向下载链接
- `page` (Page): Playwright页面对象
- `downloadDir` (string): 下载目录的绝对路径
- `fileName` (string): 文件名（不包含.pdf扩展名）
- `waitTime` (number, 可选): 等待时间，默认3000毫秒

**返回值：**
- `Promise<boolean>`: 下载是否成功

### 高级用法

如果需要更多控制，可以直接使用 `PDFDownloader` 类：

```javascript
const { PDFDownloader } = require('../utils/pdfDownloader');

const downloader = new PDFDownloader();

// 设置拦截器
await downloader.setupPDFInterceptor(page, downloadDir, fileName);

// 手动点击下载链接
await downloadLinkElement.click();

// 等待下载完成
await page.waitForTimeout(3000);

// 清理资源
downloader.cleanup();
```

## 注意事项

1. **单例模式**：工具使用单例模式，避免在同一个浏览器上下文中重复设置监听器
2. **错误处理**：工具内置了完整的错误处理，会自动记录日志
3. **文件命名**：文件名会自动添加.pdf扩展名，无需手动添加
4. **路径处理**：确保下载目录存在，工具不会自动创建目录
5. **超时设置**：默认等待时间为3秒，可根据网络情况调整

## 故障排除

### 常见问题

1. **下载失败**
   - 检查下载链接是否正确
   - 确认下载目录是否存在且有写入权限
   - 检查网络连接是否正常

2. **PDF页面未被识别**
   - 检查URL是否包含/file/getFile/
   - 可能需要调整PDF页面检测逻辑

3. **重复监听警告**
   - 使用 `resetPDFDownloader()` 重置状态
   - 或使用 `cleanupPDFDownloader()` 清理资源

### 调试技巧

- 查看控制台日志，工具会输出详细的操作信息
- 使用浏览器开发者工具检查网络请求
- 增加等待时间以适应慢速网络