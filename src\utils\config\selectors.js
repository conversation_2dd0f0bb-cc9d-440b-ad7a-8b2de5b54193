/**
 * 选择器配置 - 集中管理所有页面选择器
 */
const SELECTORS = {
  // 登录页面
  login: {
    username: '#username',
    password: '#password',
    loginBtn: 'button.large.block.login-btn',
  },

  // 搜索页面
  search: {
    input: 'input[placeholder="请输入提款申请号"]',
    button: '.search-btns .el-button.el-button--info.el-button--mini',
    detailButton: [
      '.el-table__fixed-right .el-table__fixed-body-wrapper button:has-text("详情")',
      '.el-button:has-text("详情")',
    ],
  },

  // 详情页面
  detail: {
    // 客户名称
    customerName: [
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div/form/div/div/div[2]/div/div/div/div/a', // /span
    ],
    // 放款交易时间
    LoanTransactionTime: [
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[9]/div/div/form[2]/div/div[3]/table/tbody/tr/td[8]/div/span',
    ],
    // 提款流程编号
    withdrawalNumber: [
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div/form/div/div/div[1]/div/div/span',
    ],
    // 放款交易时间
    financingApplicationLink: [
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[13]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a',
    ],
    // 放款附件
    loan: 'xpath=/html/body/div/div/div/div[2]/div/div[2]/div[9]/div/div/form[2]/div/div[3]/table/tbody/tr/td[9]/div/div/div/div/div/ul/li/a',
    print: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[1]/div/button[1]',

    // 融资申请号
    rongZiNumber:
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div/form/div/div/div[3]/div/div/div/div/a',
    // 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div/form/div/div/div[3]/div/div',

    // 关联合同
    guanLianContract:
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[12]/div/div/div/div/div[4]/div[2]/table/tbody',
    // 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[12]/div/div/div/div/div[3]/table/tbody/tr',
    // '#r4Fp7oymI6 > .pl20 > div > .the-basic-table--form > .el-table',

    // 资产详情按钮：
    ziChanDetailBtn:
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[4]/div/div/div[1]/div[2]/div[5]/div[2]/table/tbody/tr/td[26]/div/div/button[1]',

    // 资产详情文件列表按钮：
    ziChanFileListBtn: '#tab-fileList',
    // 'xpath=/html/body/div[5]/div/div[2]/div/div[1]/div/div/div/div[4]',

    // 基础交易合同
    jiChuJiaoYiContract:
      '#pane-fileList p.commonTitleLine:has-text("基础交易合同") + * .el-table__fixed-body-wrapper table tbody tr',

    // 填表人归档号
    fillerArchiveNumber:
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[5]/div/div/div/div[3]/table/tbody/tr/td[11]/div',
    // 中登查重表格行
    zhongDengChaChongRows:
      'p.commonTitleLine:has-text("中登查重") + * .el-table__body-wrapper tbody tr',

    // 到期日调整通知书
    daoQiRiFile:
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div[4]/div/form/div/div/div[3]/table/tbody/tr/td[4]/div/div/div/div/a',
  },

  // 表格相关
  table: {
    rows: '.el-table__body-wrapper tr.el-table__row',
    resultCheck: (targetNumber) => {
      return `//tr[contains(., "${targetNumber}")]`;
    },
  },
};

module.exports = SELECTORS;
