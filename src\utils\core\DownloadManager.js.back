const path = require('path');
const logger = require('../logger');

/**
 * 下载管理类 - 处理文件下载
 */
class DownloadManager {
  constructor(page, options = {}) {
    this.page = page;
    this.options = {
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000,
      ...options
    };
  }

  /**
   * 下载文件
   * @param {string} linkSelector - 下载链接选择器
   * @param {string} targetDir - 目标目录
   * @param {string} fileName - 文件名（不含扩展名）
   * @param {string} fileExtension - 文件扩展名，默认为 'pdf'
   */
  async downloadFile(linkSelector, targetDir, fileName, fileExtension = 'pdf') {
    try {
      // 等待下载链接出现
      const downloadLink = this.page.locator(linkSelector);
      await downloadLink.waitFor({ state: 'visible', timeout: this.options.timeout });
      
      logger.success(`成功找到下载链接: ${fileName}`);

      // 设置下载路径
      const fullFileName = `${fileName}.${fileExtension}`;
      const downloadPath = path.join(targetDir, fullFileName);

      // 监听下载事件
      const downloadPromise = this.page.waitForEvent('download', { 
        timeout: this.options.timeout 
      });

      // 点击下载链接
      await downloadLink.click();

      // 等待下载完成
      const download = await downloadPromise;
      
      // 保存文件到指定路径
      await download.saveAs(downloadPath);
      
      logger.success(`文件下载完成: ${downloadPath}`);
      return true;

    } catch (error) {
      logger.error(`下载文件失败: ${fileName}`, error);
      return false;
    }
  }

  /**
   * 批量下载文件
   * @param {Array} downloadTasks - 下载任务数组
   * @param {string} targetDir - 目标目录
   */
  async downloadMultiple(downloadTasks, targetDir) {
    const results = [];
    
    for (const task of downloadTasks) {
      const { selector, fileName, fileExtension } = task;
      const success = await this.downloadFile(selector, targetDir, fileName, fileExtension);
      results.push({ fileName, success });
      
      // 下载间隔
      if (downloadTasks.indexOf(task) < downloadTasks.length - 1) {
        await this.page.waitForTimeout(this.options.retryDelay);
      }
    }
    
    return results;
  }
}

module.exports = DownloadManager;