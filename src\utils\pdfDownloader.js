const path = require('path');
const fs = require('fs');
const logger = require('./logger');

const isFilePage = (url) => {
  return (
    url.includes('/file/get-file-with-token/') || url.includes('/file/getFile/')
  );
};

/**
 * PDF下载工具类
 * 用于处理PDF预览页面的拦截和文件下载
 */
class PDFDownloader {
  constructor() {
    this.downloadPromise = null;
    this.isListening = false;
    this.downloadCompleted = false;
    // 保存监听器引用，用于清理
    this.pageListener = null;
    this.responseListener = null;
  }

  /**
   * 设置PDF下载拦截器
   * @param {Page} page - Playwright页面对象
   * @param {string} downloadDir - 下载目录路径
   * @param {string} fileName - 文件名（不包含扩展名）
   * @returns {Promise<void>}
   */
  async setupPDFInterceptor(page, downloadDir, fileName) {
    const context = page.context();

    // 避免重复监听
    if (this.isListening) {
      return;
    }

    this.isListening = true;

    // 保存监听器引用
    this.pageListener = async (newPage) => {
      try {
        await newPage.waitForLoadState('load', { timeout: 10000 });
        const url = newPage.url();
        if (isFilePage(url)) {
          await this.handlePDFDownload(newPage, downloadDir, fileName);
        }
      } catch (newPageError) {
        logger.warn(`处理新页面失败: ${newPageError.message}`);
      }
    };

    this.responseListener = async (response) => {
      try {
        const url = response.url();
        if (isFilePage(url) && response.status() === 200) {
          const contentType = response.headers()['content-type'];
          if (contentType && contentType.includes('application/pdf')) {
            await this.handleDirectPDFDownload(
              page,
              response,
              downloadDir,
              fileName,
            );
          }
        }
      } catch (responseError) {
        logger.warn(`⚠️ 处理响应失败: ${responseError.message}`);
      }
    };

    // 绑定监听器
    context.on('page', this.pageListener);
    page.on('response', this.responseListener);

    // 保存 page 和 context 引用用于清理
    this.page = page;
    this.context = context;
  }

  /**
   * 处理PDF页面下载
   */
  async handlePDFDownload(pdfPage, downloadDir, fileName) {
    try {
      // 设置下载处理
      this.downloadPromise = pdfPage.waitForEvent('download', {
        timeout: 30000,
      });

      // 触发下载
      await pdfPage.evaluate(() => {
        // 创建一个隐藏的下载链接
        const link = document.createElement('a');
        link.href = window.location.href;
        link.download = 'document.pdf';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });

      // 等待下载完成
      try {
        const download = await this.downloadPromise;
        const name = fileName.trim();
        const fullFileName = name.endsWith('.pdf') ? name : `${name}.pdf`;
        const downloadPath = path.join(downloadDir, fullFileName);
        await download.saveAs(downloadPath);
        logger.success(`${name} 下载成功`);
      } catch (downloadError) {
        logger.warn(`下载文件失败: ${downloadError.message}`);
      }

      // 关闭PDF预览页面
      await pdfPage.close();
    } catch (error) {
      logger.warn(`处理PDF下载失败: ${error.message}`);
    }
  }

  /**
   * 处理直接PDF下载（通过响应拦截）
   */
  async handleDirectPDFDownload(page, response, downloadDir, fileName) {
    try {
      // 获取PDF内容
      const buffer = await response.body();

      // 保存文件
      const fullFileName = fileName.endsWith('.pdf')
        ? fileName
        : `${fileName}.pdf`;
      const downloadPath = path.join(downloadDir, fullFileName);

      // 确保目录存在
      const dir = path.dirname(downloadPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(downloadPath, buffer);

      // 立即停止页面加载，避免继续加载PDF预览
      await page.evaluate(() => {
        window.stop();
        // 替换页面内容，避免PDF预览
        document.open();
        document.write(
          '<html><body><h1>文件下载完成</h1><script>window.close();</script></body></html>',
        );
        document.close();
      });

      // 标记下载完成
      this.downloadCompleted = true;
    } catch (error) {
      logger.warn(`直接下载PDF失败: ${error.message}`);
    }
  }

  /**
   * 点击下载链接并处理PDF下载
   * @param {Locator} downloadLinkElement - 下载链接元素
   * @param {Page} page - Playwright页面对象
   * @param {string} downloadDir - 下载目录路径
   * @param {string} fileName - 文件名（不包含扩展名）
   * @param {number} waitTime - 等待时间（毫秒），默认3000
   * @returns {Promise<boolean>} 是否下载成功
   */
  async downloadPDF(
    downloadLocator,
    page,
    downloadDir,
    fileName,
    waitTime = 3000,
  ) {
    try {
      // 设置PDF拦截器
      await this.setupPDFInterceptor(page, downloadDir, fileName);

      const downloadLinkElement =
        typeof downloadLocator === 'string'
          ? page.locator(downloadLocator)
          : downloadLocator;

      await downloadLinkElement.waitFor({
        state: 'attached', // 'visible',
        timeout: 10000,
      });

      // 检查元素是否可见
      // const isVisible = await downloadLinkElement.isVisible();

      // * 原方法，发现click 不如evaluate 好用。直接改成 evaluate
      // if (isVisible) {
      //   // 元素可见，使用常规点击
      //   await downloadLinkElement.click({ force: true });
      // } else {
      //   await downloadLinkElement.evaluate((el) => el.click());
      // }

      // 新方法，需进一步验证
      await downloadLinkElement.evaluate((el) => el.click());

      // 等待一段时间确保下载处理完成
      await page.waitForTimeout(waitTime);

      return true;
    } catch (error) {
      logger.warn(`PDF下载失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 重置监听状态
   */
  resetListening() {
    this.isListening = false;
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 移除事件监听器
    if (this.pageListener && this.context) {
      this.context.off('page', this.pageListener);
    }
    if (this.responseListener && this.page) {
      this.page.off('response', this.responseListener);
    }

    // 重置状态
    this.downloadPromise = null;
    this.isListening = false;
    this.downloadCompleted = false;
    this.pageListener = null;
    this.responseListener = null;
    this.page = null;
    this.context = null;
  }
}

// 创建单例实例
const pdfDownloader = new PDFDownloader();

/**
 * 简化的PDF下载函数
 * @param {string} downloadSelector - 下载链接元素
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @param {number} waitTime - 等待时间（毫秒），默认3000
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadPDFFile(
  downloadSelector,
  page,
  downloadDir,
  fileName,
  waitTime = 3000,
) {
  // 为每次下载创建独立的实例
  const downloader = new PDFDownloader();

  try {
    const result = await downloader.downloadPDF(
      downloadSelector,
      page,
      downloadDir,
      fileName,
      waitTime,
    );

    // 下载完成后清理资源
    downloader.cleanup();
    if (!result) {
      logger.warn(`${fileName}下载失败`);
    }
    return result;
  } catch (error) {
    // 出错时也要清理资源
    downloader.cleanup();
    logger.warn(`下载文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 重置PDF下载器状态
 */
function resetPDFDownloader() {
  pdfDownloader.resetListening();
}

/**
 * 清理PDF下载器资源
 */
function cleanupPDFDownloader() {
  pdfDownloader.cleanup();
}

/**
 * 从PDF预览URL下载文件（使用新页面下载方案）
 * @param {string} url - PDF预览地址
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadFromURL(url, page, downloadDir, fileName) {
  let newContext = null;
  let newPage = null;

  try {
    // 参数验证
    if (!url || !page || !downloadDir || !fileName) {
      throw new Error(
        `参数不完整: url=${url}, downloadDir=${downloadDir}, fileName=${fileName}`,
      );
    }

    // 从浏览器实例创建新的上下文和页面
    const browser = page.context().browser();
    newContext = await browser.newContext();
    newPage = await newContext.newPage();

    // 设置响应拦截器
    let downloadCompleted = false;

    newPage.on('response', async (response) => {
      try {
        const responseUrl = response.url();
        if (isFilePage(responseUrl) && response.status() === 200) {
          // 检查响应类型是否为PDF
          const contentType = response.headers()['content-type'];
          if (contentType && contentType.includes('application/pdf')) {
            // 直接处理PDF下载
            const buffer = await response.body();

            // 保存文件
            const fullFileName = `${fileName}.pdf`;
            const downloadPath = path.join(downloadDir, fullFileName);

            // 确保目录存在
            const dir = path.dirname(downloadPath);
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir, { recursive: true });
            }

            fs.writeFileSync(downloadPath, buffer);

            downloadCompleted = true;
          }
        }
      } catch (responseError) {
        logger.warn(`处理响应失败: ${responseError.message}`);
      }
    });

    // 访问PDF地址
    await newPage.goto(url, {
      timeout: 15000,
      waitUntil: 'domcontentloaded',
    });

    // 等待下载完成
    let waitCount = 0;
    while (!downloadCompleted && waitCount < 20) {
      // 最多等待10秒
      await newPage.waitForTimeout(500);
      waitCount++;
    }

    if (downloadCompleted) {
      return true;
    } else {
      logger.warn(`PDF文件下载超时: ${fileName}`);
      return false;
    }
  } catch (error) {
    logger.warn(`从URL下载PDF失败: ${error.message}`);
    return false;
  } finally {
    // 确保新页面和上下文被关闭
    if (newPage) {
      try {
        await newPage.close();
      } catch (closeError) {
        logger.warn(`关闭新页面失败: ${closeError.message}`);
      }
    }

    if (newContext) {
      try {
        await newContext.close();
      } catch (closeError) {
        logger.warn(`关闭新上下文失败: ${closeError.message}`);
      }
    }
  }
}

/**
 * 处理打印按钮点击事件
 * @param {Locator} printLocator - 打印按钮元素
 * @param {Page} page - Playwright页面对象
 * @param {string} savePath - 文件保存目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @returns {Promise<boolean>} 是否保存成功
 */
async function clickAndPrint(printLocator, page, savePath, fileName) {
  try {
    const printBtn =
      typeof printLocator === 'string'
        ? page.locator(printLocator)
        : printLocator;
    const context = page.context();

    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');

    // 点击打印按钮
    await printBtn.click();

    // 等待新页面打开
    const newPage = await newPagePromise;

    // 等待新页面加载完成 - 使用更宽松的等待策略
    try {
      // 首先等待基本加载完成
      await newPage.waitForLoadState('load', { timeout: 10000 });
    } catch (loadError) {
      logger.warn('页面加载等待超时，尝试继续处理');
    }
    // 强制等待3秒钟
    await newPage.waitForTimeout(3000); // 额外等待确保内容完全渲染

    // 生成PDF文件路径
    const pdfPath = path.join(savePath, `${fileName}.pdf`);

    // 使用page.pdf()保存PDF文件
    await newPage.pdf({
      path: pdfPath,
      format: 'A4',
      printBackground: true,
      margin: {
        top: '1cm',
        right: '1cm',
        bottom: '1cm',
        left: '1cm',
      },
    });

    logger.success(`${fileName} 下载成功`);

    // 关闭新页面
    await newPage.close();

    return true;
  } catch (error) {
    logger.warn(`处理打印按钮点击失败: ${fileName}`, error);
    return false;
  }
}

module.exports = {
  PDFDownloader,
  downloadPDFFile,
  clickAndPrint,
  downloadFromURL,
  resetPDFDownloader,
  cleanupPDFDownloader,
};
