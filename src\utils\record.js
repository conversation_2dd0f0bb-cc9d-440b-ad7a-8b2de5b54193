const fs = require('fs');
const path = require('path');
const logger = require('./logger');

class RecordManager {
  constructor(scriptName) {
    this.scriptName = scriptName;
    this.recordsDir = path.join(__dirname, '../../data/records');
    this.ensureDirectoryExists();
    
    // 生成记录文件名（按日期）
    const date = new Date().toISOString().split('T')[0];
    this.recordPath = path.join(this.recordsDir, `${scriptName}_${date}.json`);
    
    this.records = this.loadRecords();
  }

  /**
   * 确保记录目录存在
   */
  ensureDirectoryExists() {
    if (!fs.existsSync(this.recordsDir)) {
      fs.mkdirSync(this.recordsDir, { recursive: true });
    }
  }

  /**
   * 加载记录文件
   */
  loadRecords() {
    try {
      if (fs.existsSync(this.recordPath)) {
        const content = fs.readFileSync(this.recordPath, 'utf8');
        return JSON.parse(content);
      } else {
        // 创建新的记录结构
        return {
          scriptName: this.scriptName,
          createdAt: new Date().toISOString(),
          lastRunTime: null,
          processedItems: [],
          statistics: {
            totalProcessed: 0,
            successCount: 0,
            failedCount: 0,
            skippedCount: 0
          }
        };
      }
    } catch (error) {
      logger.error('加载记录文件失败', error);
      return this.createEmptyRecord();
    }
  }

  /**
   * 创建空记录结构
   */
  createEmptyRecord() {
    return {
      scriptName: this.scriptName,
      createdAt: new Date().toISOString(),
      lastRunTime: null,
      processedItems: [],
      statistics: {
        totalProcessed: 0,
        successCount: 0,
        failedCount: 0,
        skippedCount: 0
      }
    };
  }

  /**
   * 保存记录到文件
   */
  save() {
    try {
      this.records.lastRunTime = new Date().toISOString();
      fs.writeFileSync(
        this.recordPath,
        JSON.stringify(this.records, null, 2),
        'utf8'
      );
      logger.debug('记录已保存', { path: this.recordPath });
    } catch (error) {
      logger.error('保存记录失败', error);
    }
  }

  /**
   * 获取已处理的ID列表
   * @returns {Array<string>} ID数组
   */
  getProcessedIds() {
    return this.records.processedItems.map(item => item.id);
  }

  /**
   * 检查项目是否已处理
   * @param {string} itemId - 项目ID
   * @returns {boolean}
   */
  isProcessed(itemId) {
    return this.getProcessedIds().includes(itemId);
  }

  /**
   * 添加处理记录
   * @param {Object} item - 处理的项目
   * @param {string} item.id - 项目ID（必须）
   * @param {string} item.title - 项目标题
   * @param {boolean} success - 是否处理成功
   * @param {Object} extra - 额外信息
   */
  addRecord(item, success = true, extra = {}) {
    if (!item.id) {
      logger.warn('添加记录失败：缺少项目ID', { item });
      return;
    }

    // 检查是否已经处理过
    if (this.isProcessed(item.id)) {
      logger.debug('项目已处理过，跳过', { id: item.id });
      this.records.statistics.skippedCount++;
      return;
    }

    const record = {
      id: item.id,
      title: item.title || '',
      processTime: new Date().toISOString(),
      success,
      ...extra
    };

    this.records.processedItems.push(record);
    this.records.statistics.totalProcessed++;
    
    if (success) {
      this.records.statistics.successCount++;
    } else {
      this.records.statistics.failedCount++;
    }

    // 自动保存
    this.save();
    
    logger.info('添加处理记录', {
      id: item.id,
      success,
      total: this.records.statistics.totalProcessed
    });
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    return { ...this.records.statistics };
  }

  /**
   * 获取待处理的项目
   * @param {Array} items - 所有项目列表
   * @param {number} limit - 限制数量
   * @returns {Array} 待处理项目列表
   */
  getUnprocessedItems(items, limit = null) {
    const processedIds = this.getProcessedIds();
    const unprocessed = items.filter(item => !processedIds.includes(item.id));
    
    if (limit && limit > 0) {
      return unprocessed.slice(0, limit);
    }
    
    return unprocessed;
  }

  /**
   * 重置记录（慎用）
   */
  reset() {
    this.records = this.createEmptyRecord();
    this.save();
    logger.warn('记录已重置', { scriptName: this.scriptName });
  }

  /**
   * 获取历史记录文件列表
   */
  static getHistoryFiles(scriptName) {
    const recordsDir = path.join(__dirname, '../../data/records');
    
    try {
      const files = fs.readdirSync(recordsDir);
      return files
        .filter(file => file.startsWith(`${scriptName}_`) && file.endsWith('.json'))
        .map(file => ({
          filename: file,
          path: path.join(recordsDir, file),
          date: file.replace(`${scriptName}_`, '').replace('.json', '')
        }))
        .sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      logger.error('获取历史记录文件失败', error);
      return [];
    }
  }
}

module.exports = RecordManager;
